import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { SlideInForm } from '@/shared/components/common';
import { CloudStorageProviderList, CloudStorageProviderForm } from '../cloud-storage/components';
import type { CloudStorageProviderConfiguration } from '../cloud-storage/types';

/**
 * Trang quản lý tích hợp Cloud Storage
 */
const CloudStorageIntegrationPage: React.FC = () => {
  const { t } = useTranslation(['integration', 'common']);

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingProvider, setEditingProvider] = useState<CloudStorageProviderConfiguration | null>(null);

  const handleCreateNew = () => {
    setEditingProvider(null);
    setShowCreateForm(true);
  };

  const handleEdit = (provider: CloudStorageProviderConfiguration) => {
    setEditingProvider(provider);
    setShowCreateForm(true);
  };

  const handleFormSuccess = () => {
    setShowCreateForm(false);
    setEditingProvider(null);
  };

  const handleFormCancel = () => {
    setShowCreateForm(false);
    setEditingProvider(null);
  };

  return (
    <div className="w-full bg-background text-foreground">
      {/* Main Content */}
      <CloudStorageProviderList
        onCreateNew={handleCreateNew}
        onEdit={handleEdit}
      />

      {/* Slide-in Form */}
      <SlideInForm
        open={showCreateForm}
        onClose={handleFormCancel}
        title={editingProvider
          ? t('integration:cloudStorage.form.editTitle')
          : t('integration:cloudStorage.form.createTitle')
        }
        width="lg"
      >
        <CloudStorageProviderForm
          provider={editingProvider || undefined}
          onSuccess={handleFormSuccess}
          onCancel={handleFormCancel}
        />
      </SlideInForm>
    </div>
  );
};

export default CloudStorageIntegrationPage;
